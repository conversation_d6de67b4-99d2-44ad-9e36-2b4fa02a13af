# NVDA新闻数据使用指南

## 概述

系统现已支持NVIDIA Corporation (NVDA)的本地新闻数据读取功能。NVDA新闻数据来源于Alpha Vantage，包含丰富的情感分析信息，适用于AI对冲基金系统的投资决策分析。

## 数据特点

### NVDA Alpha Vantage新闻数据
- **数据源**: Alpha Vantage
- **时间范围**: 2024年1月1日 - 2025年6月15日
- **文件数量**: 532个JSON文件
- **数据格式**: 标准Alpha Vantage新闻格式
- **情感分析**: 包含情感评分和标签

### 数据字段说明
```json
{
  "title": "新闻标题",
  "url": "新闻链接",
  "time_published": "20240101T193700",
  "authors": ["作者列表"],
  "summary": "新闻摘要",
  "overall_sentiment_score": 0.106809,
  "overall_sentiment_label": "Neutral"
}
```

## 使用方法

### 1. 基本使用

```python
from src.tools.local_news_reader import get_local_multi_source_news

# 获取NVDA新闻数据
nvda_news = get_local_multi_source_news(
    ticker="NVDA",
    limit=10,
    sources=["alpha_vantage"],
    date="2024-01-15"  # 可选，默认使用配置的偏移日期
)

print(f"获取到 {len(nvda_news)} 条NVDA新闻")

# 查看第一条新闻
if nvda_news:
    first_news = nvda_news[0]
    print(f"标题: {first_news.title}")
    print(f"情感: {first_news.sentiment}")
    print(f"摘要: {first_news.summary}")
```

### 2. 配置NVDA数据源

```python
from src.config.news_config import news_config

# 检查NVDA配置
nvda_dir = news_config.get_local_data_directory("alpha_vantage", "NVDA")
print(f"NVDA数据目录: {nvda_dir}")

# 启用本地数据模式
news_config.set_use_local_data(True)
news_config.set_selected_sources(["alpha_vantage"])
```

### 3. 在投资策略中使用

```python
from src.tools.api import get_formatted_multi_source_news

# 获取格式化的NVDA新闻（用于代理分析）
formatted_news = get_formatted_multi_source_news(
    ticker="NVDA",
    limit=15,
    sources=["alpha_vantage"],
    date="2024-03-15"
)

# 新闻数据可直接用于各种代理
# 例如：factual_news_agent, subjective_news_agent等
```

## 数据目录结构

```
NVDA_alpha_news/
├── alpha_news_2024-01-01.json    # 2024年1月1日新闻
├── alpha_news_2024-01-02.json    # 2024年1月2日新闻
├── alpha_news_2024-01-03.json    # 2024年1月3日新闻
├── ...
├── alpha_news_2024-12-31.json    # 2024年12月31日新闻
├── alpha_news_2025-01-01.json    # 2025年1月1日新闻
├── ...
└── alpha_news_2025-06-15.json    # 2025年6月15日新闻
```

## 配置示例

### 1. 仅使用NVDA数据的配置

```json
{
  "default_settings": {
    "use_local_data": true,
    "time_offset_days": 1,
    "max_articles_per_source": 15,
    "fallback_to_api": false,
    "selected_sources": ["alpha_vantage"]
  },
  "stock_specific_directories": {
    "NVDA": {
      "alpha_vantage": "NVDA_alpha_news"
    }
  }
}
```

### 2. 多股票配置（包含NVDA）

```json
{
  "default_settings": {
    "use_local_data": true,
    "time_offset_days": 1,
    "max_articles_per_source": 10,
    "fallback_to_api": false,
    "selected_sources": ["alpha_vantage"]
  },
  "stock_specific_directories": {
    "AAPL": {
      "alpha_vantage": "AAPL_alpha_news"
    },
    "MSFT": {
      "alpha_vantage": "MSFT_alpha_news"
    },
    "NVDA": {
      "alpha_vantage": "NVDA_alpha_news"
    }
  }
}
```

## 测试和验证

### 运行NVDA测试脚本

```bash
python scripts/test_nvda_news.py
```

测试脚本会验证：
- ✅ NVDA数据目录结构
- ✅ 数据文件格式
- ✅ 配置正确性
- ✅ 新闻读取功能
- ✅ 多日期数据读取

### 手动验证

```python
# 检查NVDA数据可用性
from src.config.news_config import news_config

availability = news_config.check_local_data_availability()
nvda_status = availability.get("alpha_vantage", {})
print(f"NVDA数据状态: {nvda_status.get('status', '未知')}")

# 测试读取特定日期的数据
from src.tools.local_news_reader import get_local_multi_source_news

test_dates = ["2024-01-15", "2024-06-15", "2024-12-15"]
for date in test_dates:
    news = get_local_multi_source_news(
        ticker="NVDA",
        limit=3,
        sources=["alpha_vantage"],
        date=date
    )
    print(f"{date}: {len(news)} 条新闻")
```

## 注意事项

### 1. 数据源限制
- NVDA目前只支持Alpha Vantage数据源
- 如需其他数据源，需要添加相应的数据文件和配置

### 2. 日期范围
- 数据覆盖2024年1月1日至2025年6月15日
- 超出此范围的日期查询将返回空结果

### 3. 性能优化
- 系统会自动去重相同标题的新闻
- 支持限制每次查询的新闻数量
- 建议根据实际需求设置合适的limit值

### 4. 错误处理
- 如果指定日期的数据文件不存在，系统会显示警告但不会中断
- 数据格式错误会被自动跳过
- 支持优雅降级到其他可用数据源

## 集成示例

### 在回测中使用NVDA新闻

```python
from src.backtester import Backtester
from src.config.news_config import news_config

# 配置使用本地NVDA新闻数据
news_config.set_use_local_data(True)
news_config.set_selected_sources(["alpha_vantage"])

# 运行NVDA回测
backtester = Backtester()
results = backtester.run(
    tickers=["NVDA"],
    start_date="2024-01-01",
    end_date="2024-12-31",
    initial_portfolio_value=100000
)
```

### 在实时交易中使用

```python
from src.main import run_hedge_fund

# 运行包含NVDA的投资组合
run_hedge_fund(
    tickers=["AAPL", "MSFT", "NVDA"],
    portfolio_value=100000,
    use_local_news=True
)
```

## 故障排除

### 常见问题

1. **找不到NVDA数据文件**
   ```
   ⚠️  Alpha Vantage数据文件不存在: NVDA_alpha_news/alpha_news_2024-01-01.json
   ```
   **解决方案**: 检查NVDA_alpha_news目录是否存在且包含相应日期的文件

2. **配置未生效**
   ```
   ⚠️  NVDA: 未配置数据目录
   ```
   **解决方案**: 确保在news_config.py中正确配置了NVDA的stock_specific_directories

3. **数据格式错误**
   ```
   ❌ 读取Alpha Vantage数据失败: ...
   ```
   **解决方案**: 检查JSON文件格式是否正确，确保是有效的JSON数组

### 调试命令

```python
# 检查NVDA特定配置
from src.config.news_config import news_config
print("NVDA Alpha Vantage目录:", news_config.get_local_data_directory("alpha_vantage", "NVDA"))

# 运行完整测试
import subprocess
result = subprocess.run(["python", "scripts/test_nvda_news.py"], capture_output=True, text=True)
print(result.stdout)
```

## 总结

NVDA新闻数据功能已成功集成到AI对冲基金系统中，提供：

- ✅ **完整的NVDA新闻数据支持**
- ✅ **与现有系统的无缝集成**
- ✅ **丰富的情感分析数据**
- ✅ **灵活的配置选项**
- ✅ **完善的测试和验证**

系统现在可以同时处理AAPL、MSFT和NVDA三只股票的新闻数据，为投资决策提供更全面的信息支持。
