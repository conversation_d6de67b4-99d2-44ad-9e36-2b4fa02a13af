# 本地新闻数据系统使用指南

## 概述

本系统已成功修改为支持本地新闻数据读取，避免API速率限制问题，同时提供灵活的新闻源选择和配置选项。

## 主要特性

### ✅ 已实现的功能

1. **本地数据读取**: 从预保存的JSON文件读取新闻数据
2. **多源支持**: 支持Alpha Vantage、NewsAPI、Finnhub三个新闻源
3. **时间偏移**: 可配置获取前N天的新闻数据
4. **新闻源选择**: 支持单独或组合使用不同新闻源
5. **配置管理**: 支持配置文件、环境变量、命令行参数
6. **向后兼容**: 保持与现有API响应格式完全兼容
7. **错误处理**: 完善的错误处理和回退机制

## 支持的股票

目前系统支持以下股票的本地新闻数据：

- **AAPL** (Apple Inc.) - 支持Alpha Vantage、NewsAPI、Finnhub
- **MSFT** (Microsoft Corporation) - 支持Alpha Vantage、NewsAPI、Finnhub
- **NVDA** (NVIDIA Corporation) - 支持Alpha Vantage

## 目录结构

```
项目根目录/
├── AAPL_alpha_news/           # AAPL Alpha Vantage新闻数据
│   ├── alpha_news_2025-06-14.json
│   ├── alpha_news_2025-06-15.json
│   └── ...
├── AAPL_news_api_news/        # AAPL NewsAPI新闻数据
│   ├── news_api_2025-06-14.json
│   ├── news_api_2025-06-15.json
│   └── ...
├── AAPL_finnhub_news/         # AAPL Finnhub新闻数据
│   ├── finnhub_2025-06-14.json
│   ├── finnhub_2025-06-15.json
│   └── ...
├── MSFT_alpha_news/           # MSFT Alpha Vantage新闻数据
│   ├── alpha_news_2024-01-01.json
│   ├── alpha_news_2024-01-02.json
│   └── ...
├── NVDA_alpha_news/           # NVDA Alpha Vantage新闻数据
│   ├── alpha_news_2024-01-01.json
│   ├── alpha_news_2024-01-02.json
│   └── ...
└── news_config.json           # 可选的配置文件
```

## 配置方法

### 1. 配置文件方式

创建 `news_config.json` 文件：

```json
{
  "default_settings": {
    "use_local_data": true,
    "time_offset_days": 1,
    "max_articles_per_source": 10,
    "fallback_to_api": false,
    "selected_sources": ["alpha_vantage", "newsapi", "finnhub"]
  },
  "local_data_directories": {
    "alpha_vantage": "AAPL_alpha_news",
    "newsapi": "AAPL_news_api_news",
    "finnhub": "AAPL_finnhub_news"
  }
}
```

### 2. 环境变量方式

```bash
# 设置使用本地数据
export NEWS_USE_LOCAL_DATA=true

# 设置新闻源（用逗号分隔）
export NEWS_SOURCES=alpha_vantage,newsapi,finnhub

# 设置时间偏移天数
export NEWS_TIME_OFFSET_DAYS=1
```

### 3. 命令行参数方式

```bash
python src/main.py \
  --use-local-news \
  --news-sources alpha_vantage,newsapi \
  --news-time-offset 1
```

### 4. 交互式配置

```bash
python src/main.py --interactive-news-setup
```

## 使用示例

### 基本使用

```python
from src.config.news_config import news_config, print_news_config_status
from src.tools.local_news_reader import get_local_multi_source_news

# 检查配置状态
print_news_config_status()

# 获取本地新闻数据 - AAPL示例
aapl_news = get_local_multi_source_news(
    ticker="AAPL",
    limit=10,
    sources=["alpha_vantage", "newsapi"],
    date="2025-06-14"  # 可选，默认使用配置的偏移日期
)

# 获取NVDA新闻数据
nvda_news = get_local_multi_source_news(
    ticker="NVDA",
    limit=10,
    sources=["alpha_vantage"],  # NVDA目前只支持Alpha Vantage
    date="2024-01-15"
)

print(f"获取到 AAPL: {len(aapl_news)} 条新闻")
print(f"获取到 NVDA: {len(nvda_news)} 条新闻")
```

### 配置新闻源

```python
from src.config.news_config import news_config

# 设置使用的新闻源
news_config.set_selected_sources(["alpha_vantage", "finnhub"])

# 设置时间偏移
news_config.set_time_offset_days(2)  # 使用2天前的新闻

# 启用本地数据模式
news_config.set_use_local_data(True)

# 保存配置
news_config.save_config()
```

## 新闻源说明

### Alpha Vantage
- **特点**: 包含情感分析数据
- **文件格式**: `alpha_news_YYYY-MM-DD.json`
- **数据字段**: title, summary, authors, url, sentiment

### NewsAPI
- **特点**: 新闻覆盖面广
- **文件格式**: `news_api_YYYY-MM-DD.json`
- **数据字段**: title, description, author, url, publishedAt

### Finnhub
- **特点**: 实时性强，数据量大
- **文件格式**: `finnhub_YYYY-MM-DD.json`
- **数据字段**: headline, summary, source, datetime

## 时间偏移功能

系统支持时间偏移功能，用于获取指定天数前的新闻数据：

- `time_offset_days = 0`: 使用当天数据
- `time_offset_days = 1`: 使用前一天数据（默认）
- `time_offset_days = 2`: 使用前两天数据

这个功能特别适用于回测场景，确保使用的是实验日期之前的新闻数据。

## 数据格式兼容性

本地新闻数据会被转换为标准的 `YahooFinanceNews` 格式，确保与现有代码完全兼容：

```python
class YahooFinanceNews(BaseModel):
    ticker: str
    title: str
    summary: str | None = None
    content: str | None = None
    author: str | None = None
    source: str
    date: str
    url: str
    sentiment: str | None = None
    thumbnail: str | None = None
```

## 错误处理

系统提供多层错误处理：

1. **本地数据不存在**: 显示警告信息，可选择回退到API
2. **配置错误**: 使用默认配置继续运行
3. **数据格式错误**: 跳过错误数据，继续处理其他数据
4. **导入错误**: 自动回退到原有API模式

## 性能优化

- **去重处理**: 自动去除重复的新闻标题
- **数量限制**: 支持每个源的新闻数量限制
- **缓存机制**: 利用现有的输入数据保存机制
- **延迟加载**: 只在需要时加载配置和数据

## 故障排除

### 常见问题

1. **找不到本地数据文件**
   ```
   ⚠️  Alpha Vantage数据文件不存在: AAPL_alpha_news/alpha_news_2025-06-14.json
   ```
   **解决方案**: 检查文件路径和日期格式

2. **配置导入失败**
   ```
   ⚠️  无法导入本地新闻配置，使用API模式
   ```
   **解决方案**: 检查配置文件格式或使用默认配置

3. **没有获取到新闻数据**
   ```
   ⚠️  未获取到任何新闻数据
   ```
   **解决方案**: 检查日期偏移设置和数据文件可用性

### 调试命令

```python
# 检查配置状态
from src.config.news_config import print_news_config_status
print_news_config_status()

# 检查数据可用性
from src.config.news_config import news_config
availability = news_config.check_local_data_availability()
print(availability)

# 测试数据读取
from src.tools.local_news_reader import get_local_multi_source_news
news = get_local_multi_source_news(limit=1)
print(f"测试结果: {len(news)} 条新闻")
```

## 迁移指南

从API模式迁移到本地数据模式：

1. **确保数据文件存在**: 检查三个新闻数据目录
2. **创建配置文件**: 复制 `news_config_example.json` 并修改
3. **测试配置**: 运行配置检查命令
4. **逐步迁移**: 先测试单个新闻源，再启用所有源

## 扩展支持

系统设计为可扩展的，支持添加新的新闻源：

1. 在 `news_config.py` 中添加新源配置
2. 在 `local_news_reader.py` 中添加读取函数
3. 更新源映射字典

## 总结

本地新闻数据系统成功实现了以下目标：

- ✅ **避免API限制**: 完全使用本地数据，无API调用
- ✅ **灵活配置**: 支持多种配置方式和新闻源选择
- ✅ **向后兼容**: 保持现有代码结构和数据格式
- ✅ **时间控制**: 支持时间偏移，适用于回测场景
- ✅ **错误处理**: 完善的错误处理和回退机制
- ✅ **易于使用**: 提供简单的配置和使用接口

系统现在可以稳定运行，不受API速率限制影响，同时保持了原有的功能和性能。
