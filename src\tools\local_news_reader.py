#!/usr/bin/env python3
"""
本地新闻数据读取器
从本地JSON文件读取新闻数据并转换为标准格式
"""

import json
import os
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Optional, Dict, Any
from src.data.models import YahooFinanceNews
from src.config.news_config import news_config


class LocalNewsReader:
    """本地新闻数据读取器"""
    
    def __init__(self):
        self.config = news_config
    
    def get_target_date(self, base_date: Optional[str] = None) -> str:
        """
        获取目标日期（考虑时间偏移）
        
        Args:
            base_date: 基准日期 (YYYY-MM-DD格式)，如果为None则使用当前日期
            
        Returns:
            str: 目标日期 (YYYY-MM-DD格式)
        """
        if base_date:
            base = datetime.strptime(base_date, "%Y-%m-%d")
        else:
            base = datetime.now()
        
        offset_days = self.config.get_time_offset_days()
        target_date = base - timedelta(days=offset_days)
        return target_date.strftime("%Y-%m-%d")
    
    def read_alpha_vantage_news(self, date: str, directory: str, ticker: str = "MSFT") -> List[YahooFinanceNews]:
        """
        读取Alpha Vantage新闻数据
        
        Args:
            date: 日期 (YYYY-MM-DD格式)
            directory: 数据目录路径
            
        Returns:
            List[YahooFinanceNews]: 新闻列表
        """
        file_path = Path(directory) / f"alpha_news_{date}.json"
        
        if not file_path.exists():
            print(f"⚠️  Alpha Vantage数据文件不存在: {file_path}")
            return []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            news_list = []
            # 处理两种数据格式：
            # 1. 新格式：直接是数组 [{"title": ..., "url": ..., ...}, ...]
            # 2. 旧格式：包含articles键的对象 {"articles": [...]}
            if isinstance(data, list):
                articles = data  # 新格式：直接是数组
            else:
                articles = data.get("articles", [])  # 旧格式：从articles键获取

            for article in articles:
                # 转换为标准格式
                news_item = YahooFinanceNews(
                    ticker=ticker,  # 使用传入的ticker参数
                    title=article.get("title", ""),
                    summary=article.get("summary", ""),
                    content=article.get("summary", ""),  # Alpha Vantage通常只有summary
                    author=", ".join(article.get("authors", [])) if article.get("authors") else None,
                    source="Alpha Vantage",
                    date=date,
                    url=article.get("url", ""),
                    sentiment=article.get("overall_sentiment_label"),
                    thumbnail=None
                )
                news_list.append(news_item)
            
            print(f"✅ Alpha Vantage: 读取到 {len(news_list)} 条新闻 ({date})")
            return news_list
            
        except Exception as e:
            print(f"❌ 读取Alpha Vantage数据失败: {e}")
            return []
    
    def read_newsapi_news(self, date: str, directory: str, ticker: str = "MSFT") -> List[YahooFinanceNews]:
        """
        读取NewsAPI新闻数据
        
        Args:
            date: 日期 (YYYY-MM-DD格式)
            directory: 数据目录路径
            
        Returns:
            List[YahooFinanceNews]: 新闻列表
        """
        file_path = Path(directory) / f"news_api_{date}.json"
        
        if not file_path.exists():
            print(f"⚠️  NewsAPI数据文件不存在: {file_path}")
            return []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            news_list = []
            # 处理两种数据格式：
            # 1. 新格式：直接是数组 [{"title": ..., "description": ..., ...}, ...]
            # 2. 旧格式：包含articles键的对象 {"articles": [...]}
            if isinstance(data, list):
                articles = data  # 新格式：直接是数组
            else:
                articles = data.get("articles", [])  # 旧格式：从articles键获取

            for article in articles:
                # 转换为标准格式
                news_item = YahooFinanceNews(
                    ticker=ticker,
                    title=article.get("title", ""),
                    summary=article.get("description", ""),
                    content=article.get("description", ""),
                    author=article.get("author"),
                    source="NewsAPI",
                    date=date,
                    url=article.get("url", ""),
                    sentiment=None,
                    thumbnail=None
                )
                news_list.append(news_item)
            
            print(f"✅ NewsAPI: 读取到 {len(news_list)} 条新闻 ({date})")
            return news_list
            
        except Exception as e:
            print(f"❌ 读取NewsAPI数据失败: {e}")
            return []
    
    def read_finnhub_news(self, date: str, directory: str, ticker: str = "MSFT") -> List[YahooFinanceNews]:
        """
        读取Finnhub新闻数据
        
        Args:
            date: 日期 (YYYY-MM-DD格式)
            directory: 数据目录路径
            
        Returns:
            List[YahooFinanceNews]: 新闻列表
        """
        file_path = Path(directory) / f"finnhub_{date}.json"
        
        if not file_path.exists():
            print(f"⚠️  Finnhub数据文件不存在: {file_path}")
            return []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            news_list = []
            # 处理两种数据格式：
            # 1. 新格式：直接是数组 [{"headline": ..., "summary": ..., ...}, ...]
            # 2. 旧格式：包含articles键的对象 {"articles": [...]}
            if isinstance(data, list):
                articles = data  # 新格式：直接是数组
            else:
                articles = data.get("articles", [])  # 旧格式：从articles键获取

            for article in articles:
                # 转换Unix时间戳为日期字符串
                timestamp = article.get("datetime", 0)
                if timestamp:
                    try:
                        article_date = datetime.fromtimestamp(timestamp).strftime("%Y-%m-%d")
                    except:
                        article_date = date
                else:
                    article_date = date
                
                # 转换为标准格式
                news_item = YahooFinanceNews(
                    ticker=ticker,
                    title=article.get("headline", ""),
                    summary=article.get("summary", ""),
                    content=article.get("summary", ""),
                    author=None,
                    source=article.get("source", "Finnhub"),
                    date=article_date,
                    url="",  # Finnhub数据通常没有URL
                    sentiment=None,
                    thumbnail=None
                )
                news_list.append(news_item)
            
            print(f"✅ Finnhub: 读取到 {len(news_list)} 条新闻 ({date})")
            return news_list
            
        except Exception as e:
            print(f"❌ 读取Finnhub数据失败: {e}")
            return []
    
    def get_local_news_data(
        self,
        ticker: str = "MSFT",
        date: Optional[str] = None,
        sources: Optional[List[str]] = None,
        limit: int = 10
    ) -> List[YahooFinanceNews]:
        """
        从本地文件获取新闻数据
        
        Args:
            ticker: 股票代码
            date: 目标日期 (YYYY-MM-DD格式)，如果为None则使用配置的偏移日期
            sources: 要使用的新闻源列表，如果为None则使用配置的源
            limit: 每个源的新闻数量限制
            
        Returns:
            List[YahooFinanceNews]: 聚合的新闻列表
        """
        if not self.config.is_using_local_data():
            print("⚠️  当前配置为使用API数据，不读取本地数据")
            return []
        
        # 确定目标日期
        target_date = self.get_target_date(date)
        print(f"📅 获取日期 {target_date} 的新闻数据")
        
        # 确定要使用的新闻源
        if sources is None:
            sources = self.config.get_selected_sources()
        
        all_news = []
        per_source_limit = max(1, limit // len(sources)) if sources else limit
        
        # 新闻源读取函数映射
        source_readers = {
            "alpha_vantage": self.read_alpha_vantage_news,
            "newsapi": self.read_newsapi_news,
            "finnhub": self.read_finnhub_news
        }
        
        for source in sources:
            if source not in source_readers:
                print(f"⚠️  未知新闻源: {source}")
                continue
            
            directory = self.config.get_local_data_directory(source, ticker)
            if not directory:
                print(f"⚠️  {source}: 未配置数据目录")
                continue
            
            if not Path(directory).exists():
                print(f"⚠️  {source}: 数据目录不存在 - {directory}")
                continue
            
            try:
                source_news = source_readers[source](target_date, directory, ticker)
                if source_news:
                    # 限制每个源的新闻数量
                    limited_news = source_news[:per_source_limit]
                    all_news.extend(limited_news)
                    print(f"✅ {source}: 添加了 {len(limited_news)} 条新闻")
                else:
                    print(f"⚠️  {source}: 未获取到新闻数据")
            
            except Exception as e:
                print(f"❌ {source}: 读取失败 - {e}")
        
        # 去重和排序
        if all_news:
            # 按URL去重（如果有URL的话）
            seen_titles = set()
            unique_news = []
            
            for news in all_news:
                # 使用标题作为去重标准（因为本地数据可能没有URL）
                title_key = news.title.lower().strip()
                if title_key and title_key not in seen_titles:
                    seen_titles.add(title_key)
                    unique_news.append(news)
                elif not title_key:  # 没有标题的新闻也保留
                    unique_news.append(news)
            
            # 按日期排序（最新的在前）
            unique_news.sort(key=lambda x: x.date, reverse=True)
            
            # 限制总数量
            result = unique_news[:limit]
            print(f"📰 总共获取到 {len(result)} 条去重后的新闻")
            return result
        
        print("⚠️  未获取到任何新闻数据")
        return []


# 全局实例
local_news_reader = LocalNewsReader()


def get_local_multi_source_news(
    ticker: str = "MSFT",
    limit: int = 10,
    sources: Optional[List[str]] = None,
    date: Optional[str] = None
) -> List[YahooFinanceNews]:
    """
    获取本地多源新闻数据的便捷函数
    
    Args:
        ticker: 股票代码
        limit: 新闻数量限制
        sources: 新闻源列表
        date: 目标日期
        
    Returns:
        List[YahooFinanceNews]: 新闻列表
    """
    return local_news_reader.get_local_news_data(
        ticker=ticker,
        date=date,
        sources=sources,
        limit=limit
    )


if __name__ == "__main__":
    # 测试本地新闻读取
    print("🧪 测试本地新闻数据读取")
    news_data = get_local_multi_source_news(limit=5)
    
    if news_data:
        print(f"\n📰 获取到 {len(news_data)} 条新闻:")
        for i, news in enumerate(news_data[:3], 1):
            print(f"\n{i}. {news.title}")
            print(f"   来源: {news.source}")
            print(f"   日期: {news.date}")
            print(f"   摘要: {news.summary[:100]}...")
    else:
        print("❌ 未获取到新闻数据")
