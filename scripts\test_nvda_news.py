#!/usr/bin/env python3
"""
NVDA新闻数据测试脚本
验证NVDA本地新闻数据读取功能是否正常工作
"""

import sys
import os
from datetime import datetime, timedelta
from pathlib import Path

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, project_root)


def test_nvda_directory_structure():
    """测试NVDA新闻数据目录结构"""
    print("📁 检查NVDA新闻数据目录结构...")
    
    nvda_dir = Path(project_root) / "NVDA_alpha_news"
    
    if not nvda_dir.exists():
        print(f"❌ NVDA数据目录不存在: {nvda_dir}")
        return False
    
    json_files = list(nvda_dir.glob("*.json"))
    if not json_files:
        print(f"❌ NVDA数据目录中没有JSON文件: {nvda_dir}")
        return False
    
    print(f"✅ NVDA数据目录存在，包含 {len(json_files)} 个JSON文件")
    
    # 显示一些示例文件
    sample_files = sorted([f.name for f in json_files])[:5]
    print(f"📄 示例文件: {', '.join(sample_files)}")
    
    return True


def test_nvda_data_format():
    """测试NVDA新闻数据格式"""
    print("\n📊 检查NVDA新闻数据格式...")
    
    nvda_dir = Path(project_root) / "NVDA_alpha_news"
    json_files = list(nvda_dir.glob("*.json"))
    
    if not json_files:
        print("❌ 没有找到NVDA数据文件")
        return False
    
    # 测试第一个文件
    test_file = json_files[0]
    print(f"🔍 测试文件: {test_file.name}")
    
    try:
        import json
        with open(test_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        if not isinstance(data, list):
            print("❌ 数据格式错误：应该是数组格式")
            return False
        
        if not data:
            print("⚠️  数据文件为空")
            return True
        
        # 检查第一条新闻的格式
        first_article = data[0]
        required_fields = ['title', 'url', 'time_published', 'authors', 'summary', 
                          'overall_sentiment_score', 'overall_sentiment_label']
        
        missing_fields = []
        for field in required_fields:
            if field not in first_article:
                missing_fields.append(field)
        
        if missing_fields:
            print(f"❌ 缺少必需字段: {', '.join(missing_fields)}")
            return False
        
        print(f"✅ 数据格式正确，包含 {len(data)} 条新闻")
        print(f"📰 示例标题: {first_article['title'][:60]}...")
        print(f"💭 情感分析: {first_article['overall_sentiment_label']} ({first_article['overall_sentiment_score']})")
        
        return True
        
    except Exception as e:
        print(f"❌ 读取数据文件失败: {e}")
        return False


def test_nvda_news_reader():
    """测试NVDA新闻读取器"""
    print("\n🧪 测试NVDA新闻读取器...")
    
    try:
        from src.tools.local_news_reader import get_local_multi_source_news
        from src.config.news_config import news_config
        
        # 临时启用本地数据模式
        original_use_local = news_config.is_using_local_data()
        news_config.set_use_local_data(True)
        
        try:
            # 测试读取NVDA新闻数据
            test_date = "2024-01-15"  # 使用一个测试日期
            news_data = get_local_multi_source_news(
                ticker="NVDA",
                limit=5,
                sources=["alpha_vantage"],  # 只测试alpha_vantage源
                date=test_date
            )
            
            if news_data:
                print(f"✅ 成功读取 {len(news_data)} 条NVDA新闻")
                
                # 显示第一条新闻的详情
                first_news = news_data[0]
                print(f"\n📰 示例新闻:")
                print(f"  股票代码: {first_news.ticker}")
                print(f"  标题: {first_news.title[:80]}...")
                print(f"  来源: {first_news.source}")
                print(f"  日期: {first_news.date}")
                print(f"  摘要: {first_news.summary[:100]}...")
                
                # 验证股票代码是否正确设置
                if first_news.ticker == "NVDA":
                    print("✅ 股票代码设置正确")
                else:
                    print(f"⚠️  股票代码设置错误: 期望 'NVDA'，实际 '{first_news.ticker}'")
                
                return True
            else:
                print("❌ 未能读取到NVDA新闻数据")
                return False
                
        finally:
            # 恢复原始配置
            news_config.set_use_local_data(original_use_local)
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_nvda_config():
    """测试NVDA配置"""
    print("\n⚙️  测试NVDA配置...")
    
    try:
        from src.config.news_config import news_config
        
        # 测试获取NVDA的数据目录
        nvda_alpha_dir = news_config.get_local_data_directory("alpha_vantage", "NVDA")
        
        if nvda_alpha_dir == "NVDA_alpha_news":
            print("✅ NVDA Alpha Vantage目录配置正确")
        else:
            print(f"❌ NVDA Alpha Vantage目录配置错误: {nvda_alpha_dir}")
            return False
        
        # 测试其他股票的配置是否不受影响
        msft_alpha_dir = news_config.get_local_data_directory("alpha_vantage", "MSFT")
        if msft_alpha_dir == "MSFT_alpha_news":
            print("✅ MSFT配置未受影响")
        else:
            print(f"⚠️  MSFT配置可能受到影响: {msft_alpha_dir}")
        
        aapl_alpha_dir = news_config.get_local_data_directory("alpha_vantage", "AAPL")
        if aapl_alpha_dir == "AAPL_alpha_news":
            print("✅ AAPL配置未受影响")
        else:
            print(f"⚠️  AAPL配置可能受到影响: {aapl_alpha_dir}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False


def test_multiple_dates():
    """测试多个日期的数据读取"""
    print("\n📅 测试多个日期的NVDA数据读取...")
    
    try:
        from src.tools.local_news_reader import get_local_multi_source_news
        from src.config.news_config import news_config
        
        # 临时启用本地数据模式
        original_use_local = news_config.is_using_local_data()
        news_config.set_use_local_data(True)
        
        try:
            test_dates = ["2024-01-01", "2024-01-15", "2024-02-01", "2024-03-01"]
            successful_dates = []
            
            for test_date in test_dates:
                news_data = get_local_multi_source_news(
                    ticker="NVDA",
                    limit=3,
                    sources=["alpha_vantage"],
                    date=test_date
                )
                
                if news_data:
                    successful_dates.append(test_date)
                    print(f"✅ {test_date}: {len(news_data)} 条新闻")
                else:
                    print(f"⚠️  {test_date}: 无数据")
            
            if successful_dates:
                print(f"✅ 成功读取 {len(successful_dates)}/{len(test_dates)} 个日期的数据")
                return True
            else:
                print("❌ 所有测试日期都无法读取数据")
                return False
                
        finally:
            # 恢复原始配置
            news_config.set_use_local_data(original_use_local)
            
    except Exception as e:
        print(f"❌ 多日期测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始NVDA新闻数据测试")
    print("=" * 60)
    
    tests = [
        ("目录结构测试", test_nvda_directory_structure),
        ("数据格式测试", test_nvda_data_format),
        ("配置测试", test_nvda_config),
        ("新闻读取器测试", test_nvda_news_reader),
        ("多日期测试", test_multiple_dates),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}")
        print("-" * 40)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！NVDA新闻数据功能正常工作")
        return True
    else:
        print("⚠️  部分测试失败，请检查配置和数据")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
